'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import ContentEditable from 'react-contenteditable';
import api from '@/lib/api';
import { useSelector, useDispatch } from 'react-redux';
import { selectIsSaving, setIsSaving } from '@/store/features/diarySlice';
import FeedbackModal from '../_components/FeedbackModal';

const isHtmlEmpty = (html) => {
  if (!html) return true;
  const strippedHtml = html.replace(/<[^>]*>?/gm, '').trim();
  return strippedHtml.length === 0;
};

const MissionReview = ({ data, entryId }) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [correctionHtml, setCorrectionHtml] = useState(data?.content || '');
  const [score, setScore] = useState('');
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const contentEditableRef = useRef(null);
  const isCorrectionReviewed = data?.status === 'CONFIRMED';
  const isSaving = useSelector(selectIsSaving);

  useEffect(() => {
    setCorrectionHtml(data?.correction || '');
  }, [data?.correction]);

  const handleCorrectionChange = (evt) => {
    setCorrectionHtml(evt.target.value);
  };

  const prepareToTypeBlue = () => {
    document.execCommand('foreColor', false, 'blue');
  };

  const submitReview = async () => {
    try {
      dispatch(setIsSaving(true));
      const response = await api.post(
        `/diary/tutor/missions/entries/${entryId}/correction`,
        {
          correction: correctionHtml,
          score: parseInt(score),
        }
      );
      if (response.success) {
        router.push('/dashboard/submission-management/hec-diary?tab=missionDiary');
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      toast.error(err.message || 'Failed to submit review');
    } finally {
      dispatch(setIsSaving(false));
    }
  };

 

  return (
    <>
      <div className="rounded-md">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-2xl font-semibold text-[#723F11]">
            Tutor Correction Zone
          </h3>
        </div>

        {data?.correction?.correction ? (
          <div
            className="mb-4 p-3 border border-gray-300 rounded-lg min-h-[9rem] overflow-y-auto whitespace-pre-wrap"
            dangerouslySetInnerHTML={{ __html: data.correction.correction }}
          />
        ) : (
          <div className="mb-4">
            <ContentEditable
              innerRef={contentEditableRef}
              html={correctionHtml}
              disabled={false}
              onChange={handleCorrectionChange}
              onFocus={prepareToTypeBlue}
              onClick={prepareToTypeBlue}
              onKeyDown={(e) => {
                if (
                  e.key.length === 1 ||
                  e.key === 'Enter' ||
                  e.key === 'Backspace' ||
                  e.key === 'Delete'
                ) {
                  prepareToTypeBlue();
                }
              }}
              className="w-full p-3 border border-gray-300 rounded-lg min-h-[9rem] focus:outline-none focus:ring-2 focus:ring-yellow-300 editable-content"
              data-placeholder="Make corrections here..."
            />
            <style jsx global>{`
              .editable-content:empty::before {
                content: attr(data-placeholder);
                color: #a0aec0;
                pointer-events: none;
              }
            `}</style>
          </div>
        )}

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-5">
            <button
              onClick={() => setIsFeedbackModalOpen(true)}
              className="px-4 py-1 bg-[#FEFCE8] text-base text-[#723F11] rounded-md border border-[#723F11] font-medium hover:bg-[#FFF8D6]"
            >
              Give feedback
            </button>
            <div className="flex items-center border border-[#723F11] rounded-md overflow-hidden">
              <label className="px-3 py-1 bg-[#FEFCE8] text-[#723F11] text-base font-medium">
                Score
              </label>
              {isCorrectionReviewed ? (
                <div className="px-3 py-1 text-gray-700 bg-white">
                  {data.gainedScore}
                </div>
              ) : (
                <input
                  type="number"
                  value={score}
                  onChange={(e) => setScore(e.target.value)}
                  className="w-16 px-3 py-1 focus:outline-none text-gray-700"
                  min="0"
                  max="100"
                  placeholder="---"
                />
              )}
            </div>
          </div>
          {!isCorrectionReviewed && (
            <button
              onClick={submitReview}
              disabled={isHtmlEmpty(correctionHtml) || !score || isSaving}
              className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              {isSaving ? 'Saving...' : 'Submit Review'}
            </button>
          )}
        </div>
      </div>

      <FeedbackModal
        isOpen={isFeedbackModalOpen}
        onClose={() => setIsFeedbackModalOpen(false)}
        onSubmit={async (feedback) => {
          const response = await api.post(
            `/diary/tutor/missions/entries/${entryId}/feedback`,
            { feedback }
          );

          if (!response.success) {
            throw new Error(response.message || 'Failed to submit feedback');
          }
        }}
        title="Teachers Feedback"
        placeholder="Write here"
        submitButtonText="Confirm"
        submitButtonColor="bg-yellow-500 hover:bg-yellow-600"
      />
    </>
  );
};

export default MissionReview;
