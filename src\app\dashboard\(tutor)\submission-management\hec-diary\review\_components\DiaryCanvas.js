'use client';

import { useEffect, useCallback, useState, useRef } from 'react';
import { useDispatch } from 'react-redux';
import Canvas from '@/components/skin/Canvas';
import { formatDate } from '@/utils/dateFormatter';
import {
  setPreviewMode,
  resetCanvas,
  changeBackground,
  addImageToCanvas,
  setInitialTemplate,
  setSelectedId,
} from '@/store/features/canvasSlice';
import CanvasPreview from '@/components/skin/CanvasPreview';

const DiaryCanvas = ({ data }) => {
  const dispatch = useDispatch();
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isInitialized, setIsInitialized] = useState(false);
  const canvasWrapperRef = useRef(null);

  // Detect browser zoom level
  useEffect(() => {
    const detectZoom = () => {
      const zoom = Math.round((window.outerWidth / window.innerWidth) * 100) / 100;
      setZoomLevel(zoom);
    };

    detectZoom();
    window.addEventListener('resize', detectZoom);

    return () => window.removeEventListener('resize', detectZoom);
  }, []);

  // Function to parse and apply skin template
  const applySkinTemplate = useCallback((skin) => {
    if (!skin?.templateContent || isInitialized) return;

    try {
      // Reset canvas first
      dispatch(resetCanvas());

      // Parse template data
      const templateData = JSON.parse(skin.templateContent);

      // Set initial template using ONLY the skin's background color
      dispatch(setInitialTemplate({
        items: [],
        background: templateData.background || '#ddd', // Only use template background
        width: templateData.width || 627,
        height: templateData.height || 357
      }));

      // Add items to canvas with proper timing
      setTimeout(() => {
        if (templateData.items?.length) {
          templateData.items.forEach(item => {
            if (item.type === 'image') {
              dispatch(addImageToCanvas({
                id: item.id,
                imageSrc: item.image,
                styles: item.styles,
                zIndex: item.zIndex || 1
              }));
            } else if (item.type === 'text') {
              let content = item.content;

              // Replace content with actual diary data if needed
              if (item.textType === 'subject' && data?.title) {
                content = data.title;
              } else if (item.textType === 'body' && data?.content) {
                content = data.content;
              } else if (item.textType === 'date' && data?.entryDate) {
                content = formatDate(data.entryDate, item.dateFormat || 'ordinal');
              }

              dispatch({
                type: 'canvas/addTextItem',
                payload: {
                  ...item,
                  content,
                  styles: {
                    ...item.styles,
                    width: item.styles?.width || 300,
                    height: item.styles?.height || 40,
                    x: item.styles?.x || 50,
                    y: item.styles?.y || 20,
                    fontFamily: item.styles?.fontFamily || 'Roboto'
                  }
                }
              });
            }
          });
        }

        // Force refresh of canvas
        dispatch(setPreviewMode(false));
        setTimeout(() => {
          dispatch(setPreviewMode(true));
          // Explicitly clear selection after applying template
          dispatch(setSelectedId(null));
        }, 50);
      }, 50);

      setIsInitialized(true);
    } catch (error) {
      console.error('Error applying skin template:', error);
    }
  }, [dispatch, data, isInitialized]);

  // Initialize canvas when data is loaded
  useEffect(() => {
    if (data?.skin && !isInitialized) {
      applySkinTemplate(data.skin);
    }
  }, [data, applySkinTemplate, isInitialized]);

  // Enable preview mode and clear selection on mount
  useEffect(() => {
    dispatch(setPreviewMode(true));
    dispatch(setSelectedId(null));
  }, [dispatch]);

  return (
    <div className="">
      <div className="canvas-container">
        {data?.skin ? (
          <div
            ref={canvasWrapperRef}
            className="canvas-wrapper"
            style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              transform: `scale(${zoomLevel > 1 ? (1 / zoomLevel) * 0.9 : 1})`,
              transformOrigin: 'center'
            }}
          >
            <CanvasPreview skin={data.skin}  />
          </div>
        ) : (
          <div
            className="h-[400px] overflow-auto whitespace-pre-wrap"
            style={{ backgroundColor: data?.backgroundColor || '#ddd' }}
          >
            {data?.content}
          </div>
        )}
      </div>
    </div>
  );
};

export default DiaryCanvas;